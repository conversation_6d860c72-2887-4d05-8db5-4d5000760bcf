parameters:
	ignoreErrors:
		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/Response.php

		-
			message: "#^Strict comparison using \\=\\=\\= between null and string will always evaluate to false\\.$#"
			count: 1
			path: src/Response.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/ServerRequest.php

		-
			message: "#^Strict comparison using \\!\\=\\= between null and null will always evaluate to false\\.$#"
			count: 1
			path: src/ServerRequest.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 1
			path: src/Stream.php

		-
			message: "#^Result of && is always false\\.$#"
			count: 2
			path: src/UploadedFile.php

		-
			message: "#^Strict comparison using \\=\\=\\= between false and true will always evaluate to false\\.$#"
			count: 2
			path: src/UploadedFile.php
