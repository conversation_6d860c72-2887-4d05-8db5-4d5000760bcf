on:
  pull_request: ~
  push:
    branches:
      - "master"

name: Tests

jobs:
  phpunit:
    name: PHPUnit with PHP ${{ matrix.php-version }} on ${{ matrix.operating-system }}

    strategy:
      matrix:
        operating-system:
          - 'ubuntu-latest'
        php-version:
          - '7.2'
          - '7.3'
          - '7.4'
          - '8.0'
          - '8.1'
          - '8.2'
        include:
          - operating-system: 'windows-latest'
            php-version: '7.4'

    runs-on: ${{ matrix.operating-system }}

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install PHP with extensions
        uses: shivammathur/setup-php@v2
        with:
          coverage: none
          php-version: ${{ matrix.php-version }}

      - name: Get composer cache directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: composer-${{ runner.os }}-${{ matrix.php-version }}-${{ hashFiles('composer.*') }}
          restore-keys: |
            composer-${{ runner.os }}-${{ matrix.php-version }}-
            composer-${{ runner.os }}-
            composer-

      - name: Download dependencies
        run: composer update --no-interaction --no-progress --optimize-autoloader

      - name: Run PHPUnit
        run: ./vendor/bin/phpunit

  phpunit-lowest:
    name: PHPUnit lowest dependencies
    runs-on: ubuntu-latest

    steps:
      - name: Checkout code
        uses: actions/checkout@v3

      - name: Install PHP with extensions
        uses: shivammathur/setup-php@v2
        with:
          coverage: none
          php-version: 7.4

      - name: Download dependencies
        run: composer update --no-interaction --no-progress --optimize-autoloader --prefer-stable --prefer-lowest

      - name: Run PHPUnit
        run: ./vendor/bin/phpunit --coverage-text --coverage-clover=coverage.xml
