{"log": {"version": "1.2", "creator": {"name": "Firefox", "version": "114.0.2"}, "browser": {"name": "Firefox", "version": "114.0.2"}, "pages": [{"startedDateTime": "2023-06-28T11:34:32.977+02:00", "id": "page_1", "title": "https://symfony.com/releases.json", "pageTimings": {"onContentLoad": 95, "onLoad": 95}}], "entries": [{"pageref": "page_1", "startedDateTime": "2023-06-28T11:34:32.977+02:00", "request": {"bodySize": 0, "method": "GET", "url": "https://symfony.com/releases.json", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "symfony.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,*/*;q=0.8"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "<PERSON><PERSON>", "value": "symfony=7a16e52fc684d8e5055efc0a26foobar"}, {"name": "Upgrade-Insecure-Requests", "value": "1"}, {"name": "Sec-Fetch-Dest", "value": "document"}, {"name": "Sec-Fetch-Mode", "value": "navigate"}, {"name": "Sec-Fetch-Site", "value": "none"}, {"name": "Sec-Fetch-User", "value": "?1"}, {"name": "TE", "value": "trailers"}], "cookies": [{"name": "symfony", "value": "7a16e52fc684d8e5055efc0a26foobar"}], "queryString": [], "headersSize": 502}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/2", "headers": [{"name": "date", "value": "Wed, 28 Jun 2023 09:34:33 GMT"}, {"name": "content-type", "value": "application/json"}, {"name": "age", "value": "3386"}, {"name": "cache-control", "value": "public, s-maxage=3600"}, {"name": "permissions-policy", "value": "interest-cohort=()"}, {"name": "referrer-policy", "value": "no-referrer-when-downgrade"}, {"name": "strict-transport-security", "value": "max-age=0"}, {"name": "x-content-digest", "value": "end2d56611523c1d92b2f26cb5bfaa3ecd"}, {"name": "x-debug-info", "value": "eyJyZXRyaWVzIjowfQ=="}, {"name": "x-frame-options", "value": "deny"}, {"name": "x-platform-cache", "value": "BYPASS"}, {"name": "x-platform-cluster", "value": "lgei2rga6u3mm-master-7rqtwti"}, {"name": "x-platform-processor", "value": "mso3wvcgfq4362basj2ljveqpa"}, {"name": "x-platform-router", "value": "g3iqazcdo2xbnpwdfh436nvpna"}, {"name": "x-xss-protection", "value": "1; mode=block"}, {"name": "traceresponse", "value": "00-176cc8eb3b09a6152a8c01ff93f0eb0a-9bc17c308ca3972e-00"}, {"name": "cf-cache-status", "value": "DYNAMIC"}, {"name": "report-to", "value": "{\"endpoints\":[{\"url\":\"https:\\/\\/a.nel.cloudflare.com\\/report\\/v3?s=NqP2oqRxFSI3UbKL0%2FS0IhDnsOYRGZqHRF6ukXnzswafvnKiWllF%2BZ7lu0Bjeuegm4HNA%2FyvkrEnsoqT%2BJsUDfRtRUw2ipUoPBCO8leI2VGwgNG706IsgHn6jiwAYNUukjoToUoTG0l7\"}],\"group\":\"cf-nel\",\"max_age\":604800}"}, {"name": "nel", "value": "{\"success_fraction\":0,\"report_to\":\"cf-nel\",\"max_age\":604800}"}, {"name": "server", "value": "cloudflare"}, {"name": "cf-ray", "value": "7de4ef402b9d22a9-CDG"}, {"name": "content-encoding", "value": "br"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/vnd.mozilla.json.view", "size": 367, "encoding": "base64", "text": "eyJzeW1mb255X3ZlcnNpb25zIjp7Imx0cyI6IjUuNC4yNSIsInN0YWJsZSI6IjYuMy4xIiwibmV4dCI6IjYuNC4wLURFViJ9LCJsYXRlc3Rfc3RhYmxlX3ZlcnNpb24iOiI2LjMiLCJzdXBwb3J0ZWRfdmVyc2lvbnMiOlsiNS40IiwiNi4yIiwiNi4zIl0sIm1haW50YWluZWRfdmVyc2lvbnMiOlsiNS40IiwiNi4yIiwiNi4zIiwiNi40IiwiNy4wIl0sInNlY3VyaXR5X21haW50YWluZWRfdmVyc2lvbnMiOlsiNC40Il0sImZsZXhfc3VwcG9ydGVkX3ZlcnNpb25zIjpbIjMuNCIsIjQuMCIsIjQuMSIsIjQuMiIsIjQuMyIsIjQuNCIsIjUuMCIsIjUuMSIsIjUuMiIsIjUuMyIsIjUuNCIsIjYuMCIsIjYuMSIsIjYuMiIsIjYuMyIsIjYuNCIsIjcuMCJdfQ=="}, "redirectURL": "", "headersSize": 1100, "bodySize": 1270}, "cache": {}, "timings": {"blocked": 0, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 35, "receive": 0}, "time": 35, "_securityState": "secure", "serverIPAddress": "2606:4700:20::ac43:4826", "connection": "443"}, {"pageref": "page_1", "startedDateTime": "2023-06-28T11:34:33.073+02:00", "request": {"bodySize": 0, "method": "GET", "url": "https://symfony.com/favicon.ico", "httpVersion": "", "headers": [{"name": "Host", "value": "symfony.com"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "image/avif,image/webp,*/*"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://symfony.com/"}], "cookies": [], "queryString": [], "headersSize": 0}, "response": {"status": 0, "statusText": "", "httpVersion": "", "headers": [], "cookies": [], "content": {}, "redirectURL": "", "headersSize": 0, "bodySize": -1}, "cache": {}, "timings": {}, "time": 0}]}}