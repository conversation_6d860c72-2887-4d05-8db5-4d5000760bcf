<?php
/**
 * Web Researcher - Homepage
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration and dependencies
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'classes/Database.php';
require_once 'classes/SearchHistory.php';

// Initialize search history class
$searchHistory = new SearchHistory();

// Handle form submission
$searchResult = null;
$error = null;

if (isPost()) {
    $query = sanitizeInput(getPost('query'));
    $csrfToken = getPost(CSRF_TOKEN_NAME);
    
    if (!validateCSRFToken($csrfToken)) {
        $error = "Invalid security token. Please try again.";
    } elseif (empty($query)) {
        $error = "Please enter a search query.";
    } else {
        try {
            // For Phase 1, we'll just save the query without API response
            // In Phase 2, we'll integrate with Gemini API
            $searchId = $searchHistory->saveSearch($query, "API integration coming in Phase 2...");
            $searchResult = [
                'id' => $searchId,
                'query' => $query,
                'response' => "Search saved successfully! API integration will be added in Phase 2.",
                'created_at' => date('Y-m-d H:i:s')
            ];
            setFlashMessage('success', 'Search query saved successfully!');
        } catch (Exception $e) {
            $error = "Error saving search: " . $e->getMessage();
            error_log("Search save error: " . $e->getMessage());
        }
    }
}

// Get recent searches for display
$recentSearches = [];
try {
    $recentSearches = $searchHistory->getRecentSearches(5);
} catch (Exception $e) {
    error_log("Error fetching recent searches: " . $e->getMessage());
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - AI-Powered Research</title>
    <style>
        /* Basic styling for Phase 1 - will be enhanced in Phase 3 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .search-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .search-button {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        
        .search-button:hover {
            background: #2980b9;
        }
        
        .navigation {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: #3498db;
            text-decoration: none;
            padding: 10px 20px;
            border: 2px solid #3498db;
            border-radius: 6px;
            display: inline-block;
        }
        
        .nav-link:hover {
            background: #3498db;
            color: white;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result-card {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
        }
        
        .recent-searches {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .search-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo APP_NAME; ?></h1>
            <p>AI-Powered Research Assistant</p>
        </div>
        
        <div class="navigation">
            <a href="library.php" class="nav-link">View Search Library</a>
        </div>
        
        <?php foreach ($flashMessages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo sanitizeInput($message['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo sanitizeInput($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="search-form">
            <form method="POST" action="">
                <?php echo getCSRFTokenField(); ?>
                <input type="text" 
                       name="query" 
                       class="search-input" 
                       placeholder="Enter your research question..." 
                       value="<?php echo sanitizeInput(getPost('query', '')); ?>"
                       required>
                <button type="submit" class="search-button">Search with AI</button>
            </form>
        </div>
        
        <?php if ($searchResult): ?>
            <div class="result-card">
                <h3>Search Result</h3>
                <p><strong>Query:</strong> <?php echo sanitizeInput($searchResult['query']); ?></p>
                <p><strong>Response:</strong> <?php echo sanitizeInput($searchResult['response']); ?></p>
                <p><small>Saved at: <?php echo formatDate($searchResult['created_at']); ?></small></p>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($recentSearches)): ?>
            <div class="recent-searches">
                <h3>Recent Searches</h3>
                <?php foreach ($recentSearches as $search): ?>
                    <div class="search-item">
                        <strong><?php echo sanitizeInput(truncateText($search['query'], 80)); ?></strong>
                        <small> - <?php echo formatDate($search['created_at']); ?></small>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>
</body>
</html>
