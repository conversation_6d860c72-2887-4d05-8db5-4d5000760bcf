<?php
/**
 * Web Researcher - Homepage
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration and dependencies
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'classes/Database.php';
require_once 'classes/SearchHistory.php';
require_once 'classes/GeminiAPI.php';

// Initialize classes
$searchHistory = new SearchHistory();
$geminiAPI = new GeminiAPI();

// Handle form submission
$searchResult = null;
$error = null;

if (isPost()) {
    $query = sanitizeInput(getPost('query'));
    $csrfToken = getPost(CSRF_TOKEN_NAME);
    
    if (!validateCSRFToken($csrfToken)) {
        $error = "Invalid security token. Please try again.";
    } elseif (empty($query)) {
        $error = "Please enter a search query.";
    } else {
        try {
            // Phase 2: Save query first, then get AI response
            $searchId = $searchHistory->saveSearch($query, null);

            // Get AI response from Gemini
            $aiResult = $geminiAPI->generateContent($query, $searchId);

            if ($aiResult['success']) {
                // Update the search record with AI response
                $searchHistory->updateResponse($searchId, $aiResult['content']);

                $searchResult = [
                    'id' => $searchId,
                    'query' => $query,
                    'response' => $aiResult['content'],
                    'response_time' => $aiResult['response_time'],
                    'created_at' => date('Y-m-d H:i:s')
                ];
                setFlashMessage('success', 'Search completed successfully with AI response! (Response time: ' . $aiResult['response_time'] . 'ms)');
            } else {
                // Update with error message
                $errorResponse = "AI Response Error: " . $aiResult['error'] . "\n\nPlease try again or check your API configuration.";
                $searchHistory->updateResponse($searchId, $errorResponse);

                $searchResult = [
                    'id' => $searchId,
                    'query' => $query,
                    'response' => $errorResponse,
                    'response_time' => $aiResult['response_time'],
                    'created_at' => date('Y-m-d H:i:s')
                ];
                setFlashMessage('error', 'AI response failed: ' . $aiResult['error']);
            }
        } catch (Exception $e) {
            $error = "Error processing search: " . $e->getMessage();
            error_log("Search processing error: " . $e->getMessage());
        }
    }
}

// Get recent searches for display
$recentSearches = [];
try {
    $recentSearches = $searchHistory->getRecentSearches(5);
    // Ensure we have a valid array
    if (!is_array($recentSearches)) {
        $recentSearches = [];
    }
} catch (Exception $e) {
    error_log("Error fetching recent searches: " . $e->getMessage());
    $recentSearches = [];
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - AI-Powered Research</title>
    <style>
        /* Basic styling for Phase 1 - will be enhanced in Phase 3 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 40px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .search-form {
            background: white;
            padding: 30px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 16px;
            margin-bottom: 15px;
        }
        
        .search-button {
            background: #3498db;
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 6px;
            font-size: 16px;
            cursor: pointer;
            width: 100%;
        }
        
        .search-button:hover {
            background: #2980b9;
        }
        
        .navigation {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: #3498db;
            text-decoration: none;
            padding: 10px 20px;
            border: 2px solid #3498db;
            border-radius: 6px;
            display: inline-block;
        }
        
        .nav-link:hover {
            background: #3498db;
            color: white;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .result-card {
            background: white;
            padding: 30px;
            border-radius: 15px;
            box-shadow: 0 5px 20px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            border-left: 5px solid #3498db;
        }

        .result-card h3 {
            color: #2c3e50;
            margin-bottom: 25px;
            font-size: 1.4em;
        }

        .query-section, .response-section {
            margin-bottom: 25px;
        }

        .query-section h4, .response-section h4 {
            color: #34495e;
            margin-bottom: 10px;
            font-size: 1.1em;
        }

        .query-text {
            background: #f8f9fa;
            padding: 15px;
            border-radius: 8px;
            border-left: 4px solid #3498db;
            font-weight: 500;
            color: #2c3e50;
        }

        .response-text {
            background: #f8fffe;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e8f5e8;
            line-height: 1.7;
            color: #2c3e50;
            white-space: pre-wrap;
        }

        .meta-info {
            padding-top: 15px;
            border-top: 1px solid #eee;
            color: #666;
        }

        .loading-spinner {
            display: none;
            text-align: center;
            padding: 20px;
        }

        .loading-spinner.show {
            display: block;
        }

        .spinner {
            border: 4px solid #f3f3f3;
            border-top: 4px solid #3498db;
            border-radius: 50%;
            width: 40px;
            height: 40px;
            animation: spin 1s linear infinite;
            margin: 0 auto 15px;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        
        .recent-searches {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
        }
        
        .search-item {
            padding: 10px;
            border-bottom: 1px solid #eee;
        }
        
        .search-item:last-child {
            border-bottom: none;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1><?php echo APP_NAME; ?></h1>
            <p>AI-Powered Research Assistant</p>
        </div>
        
        <div class="navigation">
            <a href="library.php" class="nav-link">View Search Library</a>
        </div>
        
        <?php foreach ($flashMessages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo sanitizeInput($message['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <?php if ($error): ?>
            <div class="alert alert-error">
                <?php echo sanitizeInput($error); ?>
            </div>
        <?php endif; ?>
        
        <div class="search-form">
            <form method="POST" action="" id="searchForm">
                <?php echo getCSRFTokenField(); ?>
                <input type="text"
                       name="query"
                       id="searchInput"
                       class="search-input"
                       placeholder="Enter your research question..."
                       value="<?php echo sanitizeInput(getPost('query', '')); ?>"
                       required>
                <button type="submit" class="search-button" id="searchButton">
                    <span class="button-text">🔍 Search with AI</span>
                    <span class="button-loading" style="display: none;">🤖 Thinking...</span>
                </button>
            </form>
        </div>

        <div class="loading-spinner" id="loadingSpinner">
            <div class="spinner"></div>
            <p>🤖 AI is processing your query...</p>
            <p><small>This may take a few seconds</small></p>
        </div>
        
        <?php if ($searchResult): ?>
            <div class="result-card">
                <h3>🤖 AI Research Result</h3>
                <div class="query-section">
                    <h4>Your Question:</h4>
                    <p class="query-text"><?php echo sanitizeInput($searchResult['query']); ?></p>
                </div>
                <div class="response-section">
                    <h4>AI Response:</h4>
                    <div class="response-text"><?php echo nl2br(sanitizeInput($searchResult['response'])); ?></div>
                </div>
                <div class="meta-info">
                    <small>
                        📅 Generated: <?php echo formatDate($searchResult['created_at']); ?>
                        <?php if (isset($searchResult['response_time'])): ?>
                            | ⚡ Response time: <?php echo $searchResult['response_time']; ?>ms
                        <?php endif; ?>
                        | 💾 Saved to library
                    </small>
                </div>
            </div>
        <?php endif; ?>
        
        <?php if (!empty($recentSearches)): ?>
            <div class="recent-searches">
                <h3>Recent Searches</h3>
                <?php foreach ($recentSearches as $search): ?>
                    <div class="search-item">
                        <strong><?php echo sanitizeInput(truncateText($search['query'] ?? 'No query', 80)); ?></strong>
                        <small> - <?php echo formatDate($search['created_at'] ?? date('Y-m-d H:i:s')); ?></small>
                    </div>
                <?php endforeach; ?>
            </div>
        <?php endif; ?>
    </div>

    <script>
        // Enhanced form submission with loading animation
        document.getElementById('searchForm').addEventListener('submit', function(e) {
            const button = document.getElementById('searchButton');
            const buttonText = button.querySelector('.button-text');
            const buttonLoading = button.querySelector('.button-loading');
            const loadingSpinner = document.getElementById('loadingSpinner');
            const searchInput = document.getElementById('searchInput');

            // Show loading state
            buttonText.style.display = 'none';
            buttonLoading.style.display = 'inline';
            button.disabled = true;
            searchInput.disabled = true;
            loadingSpinner.classList.add('show');

            // Scroll to loading spinner
            loadingSpinner.scrollIntoView({ behavior: 'smooth', block: 'center' });
        });

        // Auto-focus search input
        document.addEventListener('DOMContentLoaded', function() {
            const searchInput = document.getElementById('searchInput');
            if (searchInput && !searchInput.value) {
                searchInput.focus();
            }
        });

        // Add enter key support for better UX
        document.getElementById('searchInput').addEventListener('keypress', function(e) {
            if (e.key === 'Enter') {
                document.getElementById('searchForm').submit();
            }
        });
    </script>
</body>
</html>
