<?php
/**
 * Database Initialization Script
 * This script will properly initialize the SQLite database
 */

echo "=== Web Researcher Database Initialization ===\n\n";

// Check SQLite support
echo "1. Checking SQLite Support:\n";
$hasPDOSQLite = extension_loaded('pdo_sqlite');
$hasSQLite3 = extension_loaded('sqlite3');

echo "   - PDO SQLite: " . ($hasPDOSQLite ? "✓ Available" : "✗ Not Available") . "\n";
echo "   - SQLite3: " . ($hasSQLite3 ? "✓ Available" : "✗ Not Available") . "\n\n";

if (!$hasPDOSQLite && !$hasSQLite3) {
    echo "❌ ERROR: No SQLite support found!\n";
    echo "Please enable either pdo_sqlite or sqlite3 extension in PHP.\n";
    exit(1);
}

// Define paths
$dbDir = __DIR__ . '/database';
$dbPath = $dbDir . '/webresearcher.db';
$sqlPath = $dbDir . '/init.sql';

echo "2. Setting up database directory:\n";
if (!is_dir($dbDir)) {
    mkdir($dbDir, 0755, true);
    echo "   ✓ Created database directory\n";
} else {
    echo "   ✓ Database directory exists\n";
}

echo "3. Reading SQL schema:\n";
if (!file_exists($sqlPath)) {
    echo "   ❌ ERROR: init.sql file not found!\n";
    exit(1);
}

$sql = file_get_contents($sqlPath);
echo "   ✓ SQL schema loaded\n";

echo "4. Initializing database:\n";

try {
    if ($hasPDOSQLite) {
        echo "   Using PDO SQLite...\n";
        $pdo = new PDO('sqlite:' . $dbPath);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->exec($sql);
        echo "   ✓ Database created with PDO\n";
        
        // Test the database
        $stmt = $pdo->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        echo "   ✓ Tables created: " . implode(', ', $tables) . "\n";
        
    } elseif ($hasSQLite3) {
        echo "   Using SQLite3...\n";
        $db = new SQLite3($dbPath);
        $db->enableExceptions(true);
        $db->exec($sql);
        echo "   ✓ Database created with SQLite3\n";
        
        // Test the database
        $result = $db->query("SELECT name FROM sqlite_master WHERE type='table'");
        $tables = [];
        while ($row = $result->fetchArray(SQLITE3_ASSOC)) {
            $tables[] = $row['name'];
        }
        echo "   ✓ Tables created: " . implode(', ', $tables) . "\n";
        $db->close();
    }
    
    echo "5. Testing database functionality:\n";
    
    // Test insert
    if ($hasPDOSQLite) {
        $stmt = $pdo->prepare("INSERT INTO search_history (query, response) VALUES (?, ?)");
        $stmt->execute(['Test query for database initialization', 'Test response to verify database is working correctly.']);
        $testId = $pdo->lastInsertId();
        echo "   ✓ Test record inserted (ID: $testId)\n";
        
        // Test select
        $stmt = $pdo->prepare("SELECT * FROM search_history WHERE id = ?");
        $stmt->execute([$testId]);
        $record = $stmt->fetch(PDO::FETCH_ASSOC);
        echo "   ✓ Test record retrieved: " . substr($record['query'], 0, 30) . "...\n";
        
        // Clean up test record
        $stmt = $pdo->prepare("DELETE FROM search_history WHERE id = ?");
        $stmt->execute([$testId]);
        echo "   ✓ Test record cleaned up\n";
        
    } else {
        $db = new SQLite3($dbPath);
        $stmt = $db->prepare("INSERT INTO search_history (query, response) VALUES (?, ?)");
        $stmt->bindValue(1, 'Test query for database initialization');
        $stmt->bindValue(2, 'Test response to verify database is working correctly.');
        $stmt->execute();
        $testId = $db->lastInsertRowID();
        echo "   ✓ Test record inserted (ID: $testId)\n";
        
        // Test select
        $stmt = $db->prepare("SELECT * FROM search_history WHERE id = ?");
        $stmt->bindValue(1, $testId);
        $result = $stmt->execute();
        $record = $result->fetchArray(SQLITE3_ASSOC);
        echo "   ✓ Test record retrieved: " . substr($record['query'], 0, 30) . "...\n";
        
        // Clean up test record
        $stmt = $db->prepare("DELETE FROM search_history WHERE id = ?");
        $stmt->bindValue(1, $testId);
        $stmt->execute();
        echo "   ✓ Test record cleaned up\n";
        $db->close();
    }
    
    echo "\n🎉 SUCCESS: Database initialized successfully!\n";
    echo "Database file: $dbPath\n";
    echo "File size: " . filesize($dbPath) . " bytes\n";
    echo "Permissions: " . substr(sprintf('%o', fileperms($dbPath)), -4) . "\n";
    
} catch (Exception $e) {
    echo "   ❌ ERROR: " . $e->getMessage() . "\n";
    exit(1);
}

echo "\n=== Database Initialization Complete ===\n";
?>
