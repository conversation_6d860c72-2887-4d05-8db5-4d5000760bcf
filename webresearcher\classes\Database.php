<?php
/**
 * Database Wrapper Class
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

class Database {
    private $db;
    private $isPDO;

    public function __construct() {
        $this->db = getDatabase();
        $this->isPDO = ($this->db instanceof PDO);
    }

    /**
     * Execute a prepared statement with parameters
     */
    public function execute($sql, $params = []) {
        try {
            $stmt = $this->db->prepare($sql);
            $stmt->execute($params);
            return $stmt;
        } catch (Exception $e) {
            error_log("Database execute error: " . $e->getMessage());
            throw new Exception("Database operation failed");
        }
    }

    /**
     * Fetch all results from a query
     */
    public function fetchAll($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetchAll();
    }

    /**
     * Fetch single result from a query
     */
    public function fetch($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->fetch();
    }

    /**
     * Insert a record and return the last insert ID
     */
    public function insert($sql, $params = []) {
        $this->execute($sql, $params);
        if ($this->isPDO) {
            return $this->db->lastInsertId();
        } else {
            return $this->db->lastInsertRowID();
        }
    }

    /**
     * Update records and return affected row count
     */
    public function update($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Delete records and return affected row count
     */
    public function delete($sql, $params = []) {
        $stmt = $this->execute($sql, $params);
        return $stmt->rowCount();
    }

    /**
     * Begin transaction
     */
    public function beginTransaction() {
        if ($this->isPDO) {
            return $this->db->beginTransaction();
        } else {
            return $this->db->exec('BEGIN TRANSACTION');
        }
    }

    /**
     * Commit transaction
     */
    public function commit() {
        if ($this->isPDO) {
            return $this->db->commit();
        } else {
            return $this->db->exec('COMMIT');
        }
    }

    /**
     * Rollback transaction
     */
    public function rollback() {
        if ($this->isPDO) {
            return $this->db->rollback();
        } else {
            return $this->db->exec('ROLLBACK');
        }
    }
}
