{"repositories": [{"type": "path", "url": "../../..", "options": {"versions": {"php-http/discovery": "99.99.x-dev"}}}], "require": {"nyholm/psr7": "*", "php-http/discovery": "99.99.x-dev", "psr/http-client": "*", "psr/http-client-implementation": "*", "slim/psr7": "*"}, "config": {"allow-plugins": {"php-http/discovery": true}}, "extra": {"discovery": {"Psr\\Http\\Client\\ClientInterface": "MyClient", "Psr\\Http\\Message\\RequestFactoryInterface": "Slim\\Psr7\\Factory\\RequestFactory"}}}