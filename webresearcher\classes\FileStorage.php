<?php
/**
 * File-based Storage System (Fallback for SQLite)
 * This provides the same interface as the database but uses JSON files
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

class FileStorage {
    private $dataFile;
    private $data;
    
    public function __construct() {
        $dataDir = __DIR__ . '/../data';
        if (!is_dir($dataDir)) {
            mkdir($dataDir, 0755, true);
        }
        
        $this->dataFile = $dataDir . '/searches.json';
        $this->loadData();
    }
    
    private function loadData() {
        if (file_exists($this->dataFile)) {
            $json = file_get_contents($this->dataFile);
            $this->data = json_decode($json, true) ?: [];
        } else {
            $this->data = [];
        }
    }
    
    private function saveData() {
        $json = json_encode($this->data, JSON_PRETTY_PRINT);
        file_put_contents($this->dataFile, $json, LOCK_EX);
    }
    
    private function getNextId() {
        $maxId = 0;
        foreach ($this->data as $item) {
            if ($item['id'] > $maxId) {
                $maxId = $item['id'];
            }
        }
        return $maxId + 1;
    }
    
    /**
     * Save a search query and response
     */
    public function saveSearch($query, $response = null) {
        $query = trim($query);
        if (empty($query)) {
            throw new InvalidArgumentException("Query cannot be empty");
        }
        
        $id = $this->getNextId();
        $search = [
            'id' => $id,
            'query' => $query,
            'response' => $response,
            'created_at' => date('Y-m-d H:i:s'),
            'updated_at' => date('Y-m-d H:i:s')
        ];
        
        $this->data[] = $search;
        $this->saveData();
        
        return $id;
    }
    
    /**
     * Update search response
     */
    public function updateResponse($id, $response) {
        foreach ($this->data as &$item) {
            if ($item['id'] == $id) {
                $item['response'] = $response;
                $item['updated_at'] = date('Y-m-d H:i:s');
                $this->saveData();
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get all search history ordered by creation date (newest first)
     */
    public function getAllSearches($limit = 100) {
        // Sort by created_at descending
        usort($this->data, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return array_slice($this->data, 0, $limit);
    }
    
    /**
     * Get search by ID
     */
    public function getSearchById($id) {
        foreach ($this->data as $item) {
            if ($item['id'] == $id) {
                return $item;
            }
        }
        return null;
    }
    
    /**
     * Search queries by keyword
     */
    public function searchQueries($keyword, $limit = 50) {
        $keyword = strtolower($keyword);
        $results = [];
        
        foreach ($this->data as $item) {
            if (strpos(strtolower($item['query']), $keyword) !== false || 
                strpos(strtolower($item['response'] ?? ''), $keyword) !== false) {
                $results[] = $item;
            }
        }
        
        // Sort by created_at descending
        usort($results, function($a, $b) {
            return strtotime($b['created_at']) - strtotime($a['created_at']);
        });
        
        return array_slice($results, 0, $limit);
    }
    
    /**
     * Delete search by ID
     */
    public function deleteSearch($id) {
        foreach ($this->data as $index => $item) {
            if ($item['id'] == $id) {
                unset($this->data[$index]);
                $this->data = array_values($this->data); // Reindex array
                $this->saveData();
                return true;
            }
        }
        return false;
    }
    
    /**
     * Get search count
     */
    public function getSearchCount() {
        return count($this->data);
    }
    
    /**
     * Get recent searches (last 10)
     */
    public function getRecentSearches($limit = 10) {
        return $this->getAllSearches($limit);
    }
}
