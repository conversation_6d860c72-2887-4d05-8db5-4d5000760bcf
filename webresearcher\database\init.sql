-- Web Researcher Database Schema
-- SQLite database initialization script

-- Create search_history table
CREATE TABLE IF NOT EXISTS search_history (
    id INTEGER PRIMARY KEY AUTOINCREMENT,
    query TEXT NOT NULL,
    response TEXT,
    created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
    updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
);

-- Create index on created_at for faster sorting
CREATE INDEX IF NOT EXISTS idx_search_history_created_at ON search_history(created_at DESC);

-- Create index on query for search functionality
CREATE INDEX IF NOT EXISTS idx_search_history_query ON search_history(query);

-- Create trigger to update updated_at timestamp
CREATE TRIGGER IF NOT EXISTS update_search_history_timestamp 
    AFTER UPDATE ON search_history
    FOR EACH ROW
BEGIN
    UPDATE search_history SET updated_at = CURRENT_TIMESTAMP WHERE id = NEW.id;
END;
