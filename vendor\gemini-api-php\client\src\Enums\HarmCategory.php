<?php

declare(strict_types=1);

namespace GeminiAPI\Enums;

enum HarmCategory: string
{
    case HARM_CATEGORY_UNSPECIFIED = 'HARM_CATEGORY_UNSPECIFIED';
    case HARM_CATEGORY_DEROGATORY = 'HARM_CATEGORY_DEROGATORY';
    case HARM_CATEGORY_TOXICITY = 'HARM_CATEGORY_TOXICITY';
    case HARM_CATEGORY_VIOLENCE = 'HARM_CATEGORY_VIOLENCE';
    case HARM_CATEGORY_SEXUAL = 'HARM_CATEGORY_SEXUAL';
    case HARM_CATEGORY_MEDICAL = 'HARM_CATEGORY_MEDICAL';
    case HARM_CATEGORY_DANGEROUS = 'HARM_CATEGORY_DANGEROUS';
    case HARM_CATEGORY_HARASSMENT = 'HARM_CATEGORY_HARASSMENT';
    case HARM_CATEGORY_HATE_SPEECH = 'HARM_CATEGORY_HATE_SPEECH';
    case HARM_CATEGORY_SEXUALLY_EXPLICIT = 'HARM_CATEGORY_SEXUALLY_EXPLICIT';
    case HARM_CATEGORY_DANGEROUS_CONTENT = 'HARM_CATEGORY_DANGEROUS_CONTENT';
}
