<?php
/**
 * Database Configuration and Connection
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

/**
 * Initialize database and create tables if they don't exist
 */
function initializeDatabase() {
    try {
        // Create database directory if it doesn't exist
        $dbDir = dirname(DB_PATH);
        if (!is_dir($dbDir)) {
            mkdir($dbDir, 0755, true);
        }

        // Try PDO first, fallback to SQLite3
        if (extension_loaded('pdo_sqlite')) {
            $pdo = new PDO('sqlite:' . DB_PATH);
            $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
            $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);

            // Read and execute initialization SQL
            if (file_exists(DB_INIT_SQL)) {
                $sql = file_get_contents(DB_INIT_SQL);
                $pdo->exec($sql);
            }

            return $pdo;
        } elseif (extension_loaded('sqlite3')) {
            // Use SQLite3 wrapper that mimics PDO interface
            return new SQLite3Wrapper(DB_PATH);
        } else {
            throw new Exception("No SQLite support available. Please enable pdo_sqlite or sqlite3 extension.");
        }
    } catch (Exception $e) {
        error_log("Database initialization error: " . $e->getMessage());
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Get database connection
 */
function getDatabase() {
    static $db = null;

    if ($db === null) {
        $db = initializeDatabase();
    }

    return $db;
}

/**
 * SQLite3 Wrapper to mimic PDO interface
 */
class SQLite3Wrapper {
    private $db;

    public function __construct($path) {
        $this->db = new SQLite3($path);
        $this->db->enableExceptions(true);

        // Initialize database schema
        if (file_exists(DB_INIT_SQL)) {
            $sql = file_get_contents(DB_INIT_SQL);
            $this->db->exec($sql);
        }
    }

    public function prepare($sql) {
        return new SQLite3StatementWrapper($this->db->prepare($sql));
    }

    public function exec($sql) {
        return $this->db->exec($sql);
    }

    public function lastInsertRowID() {
        return $this->db->lastInsertRowID();
    }
}

/**
 * SQLite3 Statement Wrapper to mimic PDOStatement interface
 */
class SQLite3StatementWrapper {
    private $stmt;
    private $result;

    public function __construct($stmt) {
        $this->stmt = $stmt;
    }

    public function execute($params = []) {
        // Bind parameters
        foreach ($params as $index => $value) {
            $this->stmt->bindValue($index + 1, $value);
        }

        $this->result = $this->stmt->execute();
        return true;
    }

    public function fetchAll() {
        $rows = [];
        while ($row = $this->result->fetchArray(SQLITE3_ASSOC)) {
            $rows[] = $row;
        }
        return $rows;
    }

    public function fetch() {
        return $this->result->fetchArray(SQLITE3_ASSOC);
    }

    public function rowCount() {
        // SQLite3 doesn't provide row count for SELECT, this is a limitation
        return 0;
    }
}
