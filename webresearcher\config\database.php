<?php
/**
 * Database Configuration and Connection - MySQL
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

/**
 * Initialize MySQL database connection
 */
function initializeDatabase() {
    try {
        // Create DSN for MySQL
        $dsn = "mysql:host=" . DB_HOST . ";charset=" . DB_CHARSET;

        // First, connect without database to create it if needed
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

        // Create database if it doesn't exist
        $pdo->exec("CREATE DATABASE IF NOT EXISTS " . DB_NAME . " CHARACTER SET " . DB_CHARSET . " COLLATE " . DB_CHARSET . "_unicode_ci");

        // Now connect to the specific database
        $dsn = "mysql:host=" . DB_HOST . ";dbname=" . DB_NAME . ";charset=" . DB_CHARSET;
        $pdo = new PDO($dsn, DB_USER, DB_PASS);
        $pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $pdo->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $pdo->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);

        // Execute initialization SQL if file exists
        if (file_exists(DB_INIT_SQL)) {
            $sql = file_get_contents(DB_INIT_SQL);
            // Split SQL into individual statements
            $statements = explode(';', $sql);
            foreach ($statements as $statement) {
                $statement = trim($statement);
                if (!empty($statement)) {
                    $pdo->exec($statement);
                }
            }
        }

        return $pdo;
    } catch (PDOException $e) {
        error_log("MySQL database initialization error: " . $e->getMessage());
        throw new Exception("Database connection failed: " . $e->getMessage());
    }
}

/**
 * Get database connection
 */
function getDatabase() {
    static $pdo = null;

    if ($pdo === null) {
        $pdo = initializeDatabase();
    }

    return $pdo;
}

/**
 * Test database connection
 */
function testDatabaseConnection() {
    try {
        $pdo = getDatabase();
        $stmt = $pdo->query("SELECT 1 as test");
        $result = $stmt->fetch();
        return $result['test'] === 1;
    } catch (Exception $e) {
        error_log("Database connection test failed: " . $e->getMessage());
        return false;
    }
}

/**
 * Get database info
 */
function getDatabaseInfo() {
    try {
        $pdo = getDatabase();
        $info = [];

        // Get MySQL version
        $stmt = $pdo->query("SELECT VERSION() as version");
        $result = $stmt->fetch();
        $info['mysql_version'] = $result['version'];

        // Get database name
        $info['database_name'] = DB_NAME;

        // Get tables
        $stmt = $pdo->query("SHOW TABLES");
        $tables = $stmt->fetchAll(PDO::FETCH_COLUMN);
        $info['tables'] = $tables;

        return $info;
    } catch (Exception $e) {
        error_log("Failed to get database info: " . $e->getMessage());
        return null;
    }
}
