<?php
/**
 * Google Gemini AI API Service Class - Simple Implementation
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

// Load the simple Gemini client
require_once __DIR__ . '/SimpleGeminiClient.php';

class GeminiAPI {
    private $client;

    public function __construct() {
        // Initialize the simple Gemini API client
        $this->client = new SimpleGeminiClient(GEMINI_API_KEY);
    }
    
    /**
     * Generate content using simple Gemini client
     */
    public function generateContent($prompt, $searchId = null) {
        return $this->client->generateContent($prompt, $searchId);
    }
    
    /**
     * Format the prompt for better AI responses
     */
    private function formatPrompt($prompt) {
        $formattedPrompt = "You are a helpful research assistant. Please provide a comprehensive, well-structured, and informative response to the following query. Use clear formatting with bullet points or numbered lists where appropriate.\n\n";
        $formattedPrompt .= "Query: " . $prompt . "\n\n";
        $formattedPrompt .= "Please provide a detailed response that includes:\n";
        $formattedPrompt .= "- Key information and facts\n";
        $formattedPrompt .= "- Relevant examples or explanations\n";
        $formattedPrompt .= "- Practical insights or recommendations\n";
        $formattedPrompt .= "- Any important considerations or limitations\n\n";
        $formattedPrompt .= "Response:";
        
        return $formattedPrompt;
    }
    

    
    /**
     * Format the AI response for better display
     */
    private function formatResponse($content) {
        // Remove any "Response:" prefix if it exists
        $content = preg_replace('/^Response:\s*/i', '', $content);
        
        // Ensure proper line breaks
        $content = str_replace(["\r\n", "\r"], "\n", $content);
        
        // Clean up multiple consecutive line breaks
        $content = preg_replace('/\n{3,}/', "\n\n", $content);
        
        return trim($content);
    }
    
    /**
     * Log API call for monitoring and debugging
     */
    private function logApiCall($searchId, $requestData, $responseData, $responseTime, $status, $errorMessage = null) {
        try {
            $sql = "INSERT INTO api_logs (search_id, api_provider, request_data, response_data, response_time_ms, status, error_message) VALUES (?, ?, ?, ?, ?, ?, ?)";
            
            $this->db->insert($sql, [
                $searchId,
                'gemini',
                json_encode($requestData),
                json_encode($responseData),
                $responseTime,
                $status,
                $errorMessage
            ]);
        } catch (Exception $e) {
            // Log the logging error, but don't throw it
            error_log("Failed to log API call: " . $e->getMessage());
        }
    }
    
    /**
     * Get API usage statistics
     */
    public function getApiStats($days = 7) {
        try {
            $sql = "SELECT 
                        COUNT(*) as total_calls,
                        SUM(CASE WHEN status = 'success' THEN 1 ELSE 0 END) as successful_calls,
                        SUM(CASE WHEN status = 'error' THEN 1 ELSE 0 END) as failed_calls,
                        AVG(response_time_ms) as avg_response_time,
                        MAX(response_time_ms) as max_response_time,
                        MIN(response_time_ms) as min_response_time
                    FROM api_logs 
                    WHERE created_at >= DATE_SUB(NOW(), INTERVAL ? DAY)";
            
            return $this->db->fetch($sql, [$days]);
        } catch (Exception $e) {
            error_log("Failed to get API stats: " . $e->getMessage());
            return null;
        }
    }
    
    /**
     * Test API connection using official client
     */
    public function testConnection() {
        try {
            $testPrompt = "Hello, this is a test. Please respond with 'API connection successful'.";
            $result = $this->generateContent($testPrompt);

            return $result['success'] && stripos($result['content'], 'successful') !== false;
        } catch (Exception $e) {
            error_log("API connection test failed: " . $e->getMessage());
            return false;
        }
    }

    /**
     * Get available models using official client
     */
    public function getAvailableModels() {
        try {
            $response = $this->client->listModels();
            return $response->models;
        } catch (Exception $e) {
            error_log("Failed to get available models: " . $e->getMessage());
            return [];
        }
    }
}
