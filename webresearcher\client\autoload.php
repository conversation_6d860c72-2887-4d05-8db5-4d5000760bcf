<?php
/**
 * Simple autoloader for Gemini API PHP Client
 */

spl_autoload_register(function ($class) {
    // Check if the class belongs to GeminiAPI namespace
    if (strpos($class, 'GeminiAPI\\') === 0) {
        // Remove the namespace prefix
        $class = substr($class, strlen('GeminiAPI\\'));
        
        // Convert namespace separators to directory separators
        $class = str_replace('\\', DIRECTORY_SEPARATOR, $class);
        
        // Build the file path
        $file = __DIR__ . DIRECTORY_SEPARATOR . 'src' . DIRECTORY_SEPARATOR . $class . '.php';
        
        // Include the file if it exists
        if (file_exists($file)) {
            require_once $file;
        }
    }
});
?>
