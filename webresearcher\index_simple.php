<?php
/**
 * Web Researcher - Homepage (Simplified for testing)
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration
require_once 'config/config.php';
require_once 'includes/functions.php';

// Handle form submission
$searchResult = null;
$error = null;

if (isPost()) {
    $query = sanitizeInput(getPost('query'));
    $csrfToken = getPost(CSRF_TOKEN_NAME);
    
    if (!validateCSRFToken($csrfToken)) {
        $error = "Invalid security token. Please try again.";
    } elseif (empty($query)) {
        $error = "Please enter a search query.";
    } else {
        // For Phase 1 testing, just display the query
        $searchResult = [
            'query' => $query,
            'response' => "Phase 1 Complete! Your query has been received. Database integration and Gemini API will be added in Phase 2.",
            'created_at' => date('Y-m-d H:i:s')
        ];
        setFlashMessage('success', 'Application is working correctly!');
    }
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - AI-Powered Research</title>
    <style>
        /* Basic styling for Phase 1 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .container {
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .app-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            text-align: center;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
            font-size: 2.5em;
            font-weight: 700;
        }
        
        .header p {
            color: #666;
            font-size: 1.2em;
            margin-bottom: 30px;
        }
        
        .search-form {
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 100%;
            padding: 20px;
            border: 2px solid #e1e8ed;
            border-radius: 50px;
            font-size: 18px;
            margin-bottom: 20px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 20px 40px;
            border: none;
            border-radius: 50px;
            font-size: 18px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            width: 100%;
        }
        
        .search-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 10px 20px rgba(102, 126, 234, 0.3);
        }
        
        .navigation {
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: #667eea;
            text-decoration: none;
            padding: 12px 24px;
            border: 2px solid #667eea;
            border-radius: 25px;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
        }
        
        .nav-link:hover {
            background: #667eea;
            color: white;
            transform: translateY(-2px);
        }
        
        .alert {
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }
        
        .result-card {
            background: rgba(255, 255, 255, 0.9);
            padding: 30px;
            border-radius: 15px;
            margin-top: 20px;
            text-align: left;
            box-shadow: 0 10px 20px rgba(0,0,0,0.1);
        }
        
        .result-card h3 {
            color: #2c3e50;
            margin-bottom: 15px;
        }
        
        .result-card p {
            margin-bottom: 10px;
        }
        
        .phase-info {
            background: rgba(102, 126, 234, 0.1);
            padding: 20px;
            border-radius: 15px;
            margin-top: 20px;
            border-left: 4px solid #667eea;
        }
        
        .phase-info h4 {
            color: #667eea;
            margin-bottom: 10px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="app-card">
            <div class="header">
                <h1><?php echo APP_NAME; ?></h1>
                <p>AI-Powered Research Assistant</p>
            </div>
            
            <div class="navigation">
                <a href="library_simple.php" class="nav-link">View Search Library</a>
            </div>
            
            <?php foreach ($flashMessages as $message): ?>
                <div class="alert alert-<?php echo $message['type']; ?>">
                    <?php echo sanitizeInput($message['message']); ?>
                </div>
            <?php endforeach; ?>
            
            <?php if ($error): ?>
                <div class="alert alert-error">
                    <?php echo sanitizeInput($error); ?>
                </div>
            <?php endif; ?>
            
            <div class="search-form">
                <form method="POST" action="">
                    <?php echo getCSRFTokenField(); ?>
                    <input type="text" 
                           name="query" 
                           class="search-input" 
                           placeholder="Enter your research question..." 
                           value="<?php echo sanitizeInput(getPost('query', '')); ?>"
                           required>
                    <button type="submit" class="search-button">Search with AI</button>
                </form>
            </div>
            
            <?php if ($searchResult): ?>
                <div class="result-card">
                    <h3>✅ Phase 1 Test Successful!</h3>
                    <p><strong>Your Query:</strong> <?php echo sanitizeInput($searchResult['query']); ?></p>
                    <p><strong>Status:</strong> <?php echo sanitizeInput($searchResult['response']); ?></p>
                    <p><small>Processed at: <?php echo formatDate($searchResult['created_at']); ?></small></p>
                </div>
            <?php endif; ?>
            
            <div class="phase-info">
                <h4>🚀 Phase 1 Status: Core Structure Complete</h4>
                <p><strong>✅ Completed:</strong></p>
                <ul style="text-align: left; margin-left: 20px;">
                    <li>Application configuration and security setup</li>
                    <li>Basic PHP structure and routing</li>
                    <li>Form handling and CSRF protection</li>
                    <li>Input validation and sanitization</li>
                    <li>Modern, responsive UI design</li>
                    <li>Navigation between pages</li>
                </ul>
                <p style="margin-top: 15px;"><strong>📋 Next Phase:</strong> Database integration and Gemini API</p>
            </div>
        </div>
    </div>
</body>
</html>
