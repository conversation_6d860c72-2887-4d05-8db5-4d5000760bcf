<?php

declare(strict_types=1);

namespace Gemini<PERSON><PERSON>;

use Curl<PERSON><PERSON><PERSON>;
use Gemini<PERSON><PERSON>\Enums\ModelName;
use Gemini<PERSON><PERSON>\Requests\CountTokensRequest;
use Gemini<PERSON><PERSON>\Requests\EmbedContentRequest;
use Gemini<PERSON><PERSON>\Requests\GenerateContentRequest;
use Gemini<PERSON><PERSON>\Requests\GenerateContentStreamRequest;
use GeminiAPI\Responses\CountTokensResponse;
use GeminiAP<PERSON>\Responses\EmbedContentResponse;
use GeminiAPI\Responses\GenerateContentResponse;
use GeminiAPI\Responses\ListModelsResponse;

/**
 * @since v1.1.0
 */
interface ClientInterface
{
    public const API_KEY_HEADER_NAME = 'x-goog-api-key';
    public const API_VERSION_V1 = 'v1';
    public const API_VERSION_V1_BETA = 'v1beta';

    public function countTokens(CountTokensRequest $request): CountTokensResponse;
    public function generateContent(GenerateContentRequest $request): GenerateContentResponse;
    public function embedContent(EmbedContentRequest $request): EmbedContentResponse;
    public function generativeModel(ModelName|string $modelName): GenerativeModel;
    public function embeddingModel(ModelName|string $modelName): EmbeddingModel;
    public function listModels(): ListModelsResponse;
    public function withBaseUrl(string $baseUrl): self;

    /**
     * @param GenerateContentStreamRequest $request
     * @param callable(GenerateContentResponse): void $callback
     * @param CurlHandle|null $curl
     * @return void
     */
    public function generateContentStream(
        GenerateContentStreamRequest $request,
        callable $callback,
        ?CurlHandle $curl = null,
    ): void;
}
