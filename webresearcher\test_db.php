<?php
// Test database connectivity
echo "PHP Version: " . PHP_VERSION . "\n";
echo "Available PDO drivers:\n";
print_r(PDO::getAvailableDrivers());

echo "\nTesting SQLite support:\n";
if (extension_loaded('sqlite3')) {
    echo "SQLite3 extension is loaded\n";
} else {
    echo "SQLite3 extension is NOT loaded\n";
}

if (extension_loaded('pdo_sqlite')) {
    echo "PDO SQLite driver is loaded\n";
} else {
    echo "PDO SQLite driver is NOT loaded\n";
}

// Try to create a simple SQLite database
try {
    $db = new SQLite3(':memory:');
    echo "SQLite3 class works!\n";
    $db->close();
} catch (Exception $e) {
    echo "SQLite3 error: " . $e->getMessage() . "\n";
}
?>
