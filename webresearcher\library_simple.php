<?php
/**
 * Web Researcher - Search Library (Simplified for testing)
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration
require_once 'config/config.php';
require_once 'includes/functions.php';

// Mock data for Phase 1 demonstration
$mockSearches = [
    [
        'id' => 1,
        'query' => 'What are the latest developments in artificial intelligence?',
        'response' => 'This is a mock response for Phase 1 testing. In Phase 2, this will be replaced with actual Gemini AI responses.',
        'created_at' => '2025-06-14 10:30:00'
    ],
    [
        'id' => 2,
        'query' => 'How does machine learning work?',
        'response' => 'Another mock response demonstrating the expandable card functionality. The actual AI responses will be much more detailed and informative.',
        'created_at' => '2025-06-14 09:15:00'
    ],
    [
        'id' => 3,
        'query' => 'Best practices for web development',
        'response' => 'Mock response showing how the library page will display saved searches. Each card can be expanded to show the full response.',
        'created_at' => '2025-06-14 08:45:00'
    ]
];

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Search Library</title>
    <style>
        /* Enhanced styling for Phase 1 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            font-weight: 700;
            margin-bottom: 10px;
        }
        
        .header p {
            font-size: 1.2em;
            opacity: 0.9;
        }
        
        .navigation {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border: 2px solid white;
            border-radius: 25px;
            display: inline-block;
            transition: all 0.3s ease;
            font-weight: 500;
            margin: 0 10px;
        }
        
        .nav-link:hover {
            background: white;
            color: #667eea;
            transform: translateY(-2px);
        }
        
        .search-bar {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 70%;
            padding: 15px 20px;
            border: 2px solid #e1e8ed;
            border-radius: 25px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .search-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .search-button {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 25px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 500;
            cursor: pointer;
            margin-left: 15px;
            transition: all 0.3s ease;
        }
        
        .search-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            padding: 20px;
            margin-bottom: 20px;
            border-radius: 15px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }
        
        .search-card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
            transition: all 0.3s ease;
        }
        
        .search-card:hover {
            transform: translateY(-5px);
            box-shadow: 0 20px 40px rgba(0,0,0,0.15);
        }
        
        .card-header {
            padding: 25px;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: all 0.3s ease;
        }
        
        .card-header:hover {
            background: rgba(102, 126, 234, 0.05);
        }
        
        .card-content {
            padding: 0 25px 25px;
            display: none;
            animation: slideDown 0.3s ease;
        }
        
        .card-content.expanded {
            display: block;
        }
        
        @keyframes slideDown {
            from {
                opacity: 0;
                transform: translateY(-10px);
            }
            to {
                opacity: 1;
                transform: translateY(0);
            }
        }
        
        .query-text {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 8px;
            font-size: 1.1em;
        }
        
        .query-meta {
            color: #666;
            font-size: 14px;
        }
        
        .response-text {
            color: #555;
            line-height: 1.7;
            margin-bottom: 20px;
            padding: 20px;
            background: rgba(102, 126, 234, 0.05);
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-delete {
            background: linear-gradient(135deg, #e74c3c, #c0392b);
            color: white;
            padding: 10px 20px;
            border: none;
            border-radius: 20px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn-delete:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(231, 76, 60, 0.3);
        }
        
        .expand-icon {
            transition: transform 0.3s ease;
            font-size: 1.2em;
            color: #667eea;
        }
        
        .expand-icon.rotated {
            transform: rotate(180deg);
        }
        
        .stats {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 20px;
            border-radius: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
            margin-bottom: 30px;
            text-align: center;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .phase-info {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            padding: 25px;
            border-radius: 20px;
            margin-top: 30px;
            border-left: 6px solid #667eea;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .phase-info h4 {
            color: #667eea;
            margin-bottom: 15px;
            font-size: 1.3em;
        }
        
        .phase-info ul {
            margin-left: 20px;
            margin-top: 10px;
        }
        
        .phase-info li {
            margin-bottom: 5px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Search Library</h1>
            <p>Your saved research history</p>
        </div>
        
        <div class="navigation">
            <a href="index_simple.php" class="nav-link">← Back to Search</a>
        </div>
        
        <?php foreach ($flashMessages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo sanitizeInput($message['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <div class="search-bar">
            <form method="GET" action="">
                <input type="text" 
                       name="search" 
                       class="search-input" 
                       placeholder="Search your saved queries..." 
                       disabled>
                <button type="button" class="search-button" disabled>Filter (Phase 2)</button>
            </form>
        </div>
        
        <div class="stats">
            <strong>📊 Demo Data: <?php echo count($mockSearches); ?> Sample Searches</strong>
        </div>
        
        <?php foreach ($mockSearches as $search): ?>
            <div class="search-card">
                <div class="card-header" onclick="toggleCard(<?php echo $search['id']; ?>)">
                    <div>
                        <div class="query-text"><?php echo sanitizeInput(truncateText($search['query'], 80)); ?></div>
                        <div class="query-meta">
                            📅 Saved on <?php echo formatDate($search['created_at'], 'M j, Y \a\t g:i A'); ?>
                        </div>
                    </div>
                    <span class="expand-icon" id="icon-<?php echo $search['id']; ?>">▼</span>
                </div>
                <div class="card-content" id="content-<?php echo $search['id']; ?>">
                    <div class="response-text">
                        <strong>🤖 AI Response:</strong><br>
                        <?php echo nl2br(sanitizeInput($search['response'])); ?>
                    </div>
                    <div class="card-actions">
                        <button type="button" class="btn-delete" onclick="alert('Delete functionality will be implemented in Phase 2 with database integration!')">
                            🗑️ Delete (Demo)
                        </button>
                    </div>
                </div>
            </div>
        <?php endforeach; ?>
        
        <div class="phase-info">
            <h4>🎯 Phase 1 Complete: UI & Core Structure</h4>
            <p><strong>✅ Successfully Implemented:</strong></p>
            <ul>
                <li>Modern, responsive design with smooth animations</li>
                <li>Expandable search cards with hover effects</li>
                <li>Navigation between pages</li>
                <li>Form handling and security (CSRF protection)</li>
                <li>Clean, professional UI/UX patterns</li>
                <li>Mobile-responsive layout</li>
            </ul>
            <p style="margin-top: 15px;"><strong>🔄 Ready for Phase 2:</strong> Database integration & Gemini AI API</p>
        </div>
    </div>
    
    <script>
        function toggleCard(id) {
            const content = document.getElementById('content-' + id);
            const icon = document.getElementById('icon-' + id);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('rotated');
            } else {
                content.classList.add('expanded');
                icon.classList.add('rotated');
            }
        }
    </script>
</body>
</html>
