<?php
/**
 * Web Researcher Application Configuration
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

// Application settings
define('APP_NAME', 'Web Researcher');
define('APP_VERSION', '1.0.0');
define('APP_URL', 'http://localhost/webresearcher');

// Database settings
define('DB_PATH', __DIR__ . '/../database/webresearcher.db');
define('DB_INIT_SQL', __DIR__ . '/../database/init.sql');

// Security settings
define('CSRF_TOKEN_NAME', 'csrf_token');
define('SESSION_NAME', 'web_researcher_session');

// API settings (for Phase 2)
define('GEMINI_API_KEY', 'AIzaSyBvGPa1w2jwVZ6rAnbj8v38rFPWCzRMf8g');
define('GEMINI_API_URL', 'https://generativelanguage.googleapis.com/v1beta/models/gemini-pro:generateContent');

// Error reporting
error_reporting(E_ALL);
ini_set('display_errors', 1);

// Timezone
date_default_timezone_set('UTC');

// Start session
if (session_status() === PHP_SESSION_NONE) {
    session_name(SESSION_NAME);
    session_start();
}

// Generate CSRF token if not exists
if (!isset($_SESSION[CSRF_TOKEN_NAME])) {
    $_SESSION[CSRF_TOKEN_NAME] = bin2hex(random_bytes(32));
}
