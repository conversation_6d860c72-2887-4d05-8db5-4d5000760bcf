<?php
/**
 * Web Researcher - Search Library
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration and dependencies
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'classes/Database.php';
require_once 'classes/SearchHistory.php';

// Initialize search history class
$searchHistory = new SearchHistory();

// Handle delete request
if (isPost() && getPost('action') === 'delete') {
    $searchId = (int)getPost('search_id');
    $csrfToken = getPost(CSRF_TOKEN_NAME);
    
    if (!validateCSRFToken($csrfToken)) {
        setFlashMessage('error', 'Invalid security token.');
    } elseif ($searchId > 0) {
        try {
            $deleted = $searchHistory->deleteSearch($searchId);
            if ($deleted) {
                setFlashMessage('success', 'Search deleted successfully.');
            } else {
                setFlashMessage('error', 'Search not found.');
            }
        } catch (Exception $e) {
            setFlashMessage('error', 'Error deleting search: ' . $e->getMessage());
            error_log("Delete search error: " . $e->getMessage());
        }
    }
    redirect('library.php');
}

// Get search parameters
$searchKeyword = sanitizeInput(getGet('search', ''));
$page = max(1, (int)getGet('page', 1));
$limit = 20;

// Fetch searches
$searches = [];
$totalCount = 0;
try {
    if (!empty($searchKeyword)) {
        $searches = $searchHistory->searchQueries($searchKeyword, $limit);
        $totalCount = count($searches); // Simplified for Phase 1
    } else {
        $searches = $searchHistory->getAllSearches($limit);
        $totalCount = $searchHistory->getSearchCount();
    }
} catch (Exception $e) {
    setFlashMessage('error', 'Error loading search history: ' . $e->getMessage());
    error_log("Library fetch error: " . $e->getMessage());
}

// Get flash messages
$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - Search Library</title>
    <style>
        /* Basic styling for Phase 1 - will be enhanced in Phase 3 */
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background-color: #f8f9fa;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .header h1 {
            color: #2c3e50;
            margin-bottom: 10px;
        }
        
        .navigation {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: #3498db;
            text-decoration: none;
            padding: 10px 20px;
            border: 2px solid #3498db;
            border-radius: 6px;
            display: inline-block;
            margin: 0 10px;
        }
        
        .nav-link:hover {
            background: #3498db;
            color: white;
        }
        
        .search-bar {
            background: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 30px;
        }
        
        .search-input {
            width: 70%;
            padding: 12px;
            border: 2px solid #e1e8ed;
            border-radius: 6px;
            font-size: 14px;
        }
        
        .search-button {
            background: #3498db;
            color: white;
            padding: 12px 20px;
            border: none;
            border-radius: 6px;
            font-size: 14px;
            cursor: pointer;
            margin-left: 10px;
        }
        
        .search-button:hover {
            background: #2980b9;
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 6px;
        }
        
        .alert-success {
            background: #d4edda;
            color: #155724;
            border: 1px solid #c3e6cb;
        }
        
        .alert-error {
            background: #f8d7da;
            color: #721c24;
            border: 1px solid #f5c6cb;
        }
        
        .search-card {
            background: white;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            overflow: hidden;
        }
        
        .card-header {
            padding: 20px;
            border-bottom: 1px solid #eee;
            cursor: pointer;
            display: flex;
            justify-content: space-between;
            align-items: center;
        }
        
        .card-header:hover {
            background: #f8f9fa;
        }
        
        .card-content {
            padding: 20px;
            display: none;
            border-top: 1px solid #eee;
        }
        
        .card-content.expanded {
            display: block;
        }
        
        .query-text {
            font-weight: 600;
            color: #2c3e50;
            margin-bottom: 5px;
        }
        
        .query-meta {
            color: #666;
            font-size: 14px;
        }
        
        .response-text {
            margin-bottom: 20px;
        }

        .response-header {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .response-content {
            background: #f8fffe;
            padding: 20px;
            border-radius: 10px;
            border: 1px solid #e8f5e8;
            line-height: 1.7;
            color: #2c3e50;
            white-space: pre-wrap;
        }
        
        .card-actions {
            display: flex;
            gap: 10px;
        }
        
        .btn-delete {
            background: #e74c3c;
            color: white;
            padding: 8px 15px;
            border: none;
            border-radius: 4px;
            font-size: 12px;
            cursor: pointer;
        }
        
        .btn-delete:hover {
            background: #c0392b;
        }
        
        .expand-icon {
            transition: transform 0.3s ease;
        }
        
        .expand-icon.rotated {
            transform: rotate(180deg);
        }
        
        .stats {
            background: white;
            padding: 15px;
            border-radius: 8px;
            box-shadow: 0 2px 10px rgba(0,0,0,0.1);
            margin-bottom: 20px;
            text-align: center;
        }
        
        .no-results {
            text-align: center;
            padding: 40px;
            color: #666;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>Search Library</h1>
            <p>Your saved research history</p>
        </div>
        
        <div class="navigation">
            <a href="index.php" class="nav-link">← Back to Search</a>
        </div>
        
        <?php foreach ($flashMessages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo sanitizeInput($message['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <div class="search-bar">
            <form method="GET" action="">
                <input type="text" 
                       name="search" 
                       class="search-input" 
                       placeholder="Search your saved queries..." 
                       value="<?php echo sanitizeInput($searchKeyword); ?>">
                <button type="submit" class="search-button">Filter</button>
                <?php if (!empty($searchKeyword)): ?>
                    <a href="library.php" class="search-button" style="text-decoration: none; display: inline-block;">Clear</a>
                <?php endif; ?>
            </form>
        </div>
        
        <div class="stats">
            <strong>Total Searches: <?php echo $totalCount; ?></strong>
            <?php if (!empty($searchKeyword)): ?>
                | Showing results for: "<?php echo sanitizeInput($searchKeyword); ?>"
            <?php endif; ?>
        </div>
        
        <?php if (empty($searches)): ?>
            <div class="no-results">
                <h3>No searches found</h3>
                <p>
                    <?php if (!empty($searchKeyword)): ?>
                        No searches match your filter criteria.
                    <?php else: ?>
                        You haven't made any searches yet. <a href="index.php">Start searching!</a>
                    <?php endif; ?>
                </p>
            </div>
        <?php else: ?>
            <?php foreach ($searches as $search): ?>
                <div class="search-card">
                    <div class="card-header" onclick="toggleCard(<?php echo $search['id']; ?>)">
                        <div>
                            <div class="query-text"><?php echo sanitizeInput(truncateText($search['query'] ?? 'No query', 100)); ?></div>
                            <div class="query-meta">
                                Saved on <?php echo formatDate($search['created_at'] ?? date('Y-m-d H:i:s'), 'M j, Y \a\t g:i A'); ?>
                            </div>
                        </div>
                        <span class="expand-icon" id="icon-<?php echo $search['id']; ?>">▼</span>
                    </div>
                    <div class="card-content" id="content-<?php echo $search['id']; ?>">
                        <div class="response-text">
                            <div class="response-header">
                                <strong>🤖 AI Response:</strong>
                            </div>
                            <div class="response-content">
                                <?php echo nl2br(sanitizeInput($search['response'] ?? 'No response available')); ?>
                            </div>
                        </div>
                        <div class="card-actions">
                            <form method="POST" action="" style="display: inline;" 
                                  onsubmit="return confirm('Are you sure you want to delete this search?')">
                                <?php echo getCSRFTokenField(); ?>
                                <input type="hidden" name="action" value="delete">
                                <input type="hidden" name="search_id" value="<?php echo $search['id']; ?>">
                                <button type="submit" class="btn-delete">Delete</button>
                            </form>
                        </div>
                    </div>
                </div>
            <?php endforeach; ?>
        <?php endif; ?>
    </div>
    
    <script>
        function toggleCard(id) {
            const content = document.getElementById('content-' + id);
            const icon = document.getElementById('icon-' + id);
            
            if (content.classList.contains('expanded')) {
                content.classList.remove('expanded');
                icon.classList.remove('rotated');
            } else {
                content.classList.add('expanded');
                icon.classList.add('rotated');
            }
        }
    </script>
</body>
</html>
