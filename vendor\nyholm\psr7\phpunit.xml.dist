<?xml version="1.0" encoding="UTF-8"?>
<phpunit xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" colors="true" bootstrap="vendor/autoload.php" xsi:noNamespaceSchemaLocation="https://schema.phpunit.de/9.3/phpunit.xsd">
  <testsuites>
    <testsuite name="Unit tests">
      <directory>tests/</directory>
    </testsuite>
    <testsuite name="Integration tests">
      <directory>./vendor/http-interop/http-factory-tests/test</directory>
    </testsuite>
  </testsuites>
  <php>
    <const name="REQUEST_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
    <const name="RESPONSE_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
    <const name="SERVER_REQUEST_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
    <const name="UPLOADED_FILE_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
    <const name="URI_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
    <const name="STREAM_FACTORY" value="Nyholm\Psr7\Factory\Psr17Factory"/>
  </php>
</phpunit>
