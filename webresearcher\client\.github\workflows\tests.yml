name: Tests

on: ['push']

jobs:
  ci:
    runs-on: ubuntu-latest

    strategy:
      fail-fast: true
      matrix:
        php-version: [8.1, 8.2, 8.3, 8.4]
        dependency-version: [prefer-lowest, prefer-stable]

    name: Tests PHP${{ matrix.php-version }} - ${{ matrix.dependency-version }}

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Cache dependencies
        uses: actions/cache@v1
        with:
          path: ~/.composer/cache/files
          key: php-${{ matrix.php-version }}-composer-${{ hashFiles('composer.json') }}

      - name: Setup PHP
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-version }}
          coverage: xdebug

      - name: Install Composer dependencies
        run: composer update --${{ matrix.dependency-version }} --no-interaction --prefer-dist

      - name: Unit Tests
        run: composer test:unit-with-coverage
