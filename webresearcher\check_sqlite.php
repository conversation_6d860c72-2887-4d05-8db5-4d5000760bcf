<?php
echo "Checking SQLite support...\n";

if (extension_loaded('pdo_sqlite')) {
    echo "✓ PDO SQLite is available\n";
} else {
    echo "✗ PDO SQLite is NOT available\n";
}

if (extension_loaded('sqlite3')) {
    echo "✓ SQLite3 is available\n";
    
    try {
        $db = new SQLite3(':memory:');
        echo "✓ SQLite3 connection test successful\n";
        $db->close();
    } catch (Exception $e) {
        echo "✗ SQLite3 connection failed: " . $e->getMessage() . "\n";
    }
} else {
    echo "✗ SQLite3 is NOT available\n";
}

echo "\nAvailable PDO drivers:\n";
if (class_exists('PDO')) {
    print_r(PDO::getAvailableDrivers());
} else {
    echo "PDO class not available\n";
}
?>
