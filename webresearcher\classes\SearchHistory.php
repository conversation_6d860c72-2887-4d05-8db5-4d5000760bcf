<?php
/**
 * Search History Management Class
 */

// Prevent direct access
if (!defined('WEB_RESEARCHER_APP')) {
    die('Direct access not permitted');
}

class SearchHistory {
    private $storage;
    private $useDatabase;

    public function __construct() {
        // Check if SQLite is available
        $this->useDatabase = extension_loaded('pdo_sqlite') || extension_loaded('sqlite3');

        if ($this->useDatabase) {
            try {
                $this->storage = new Database();
            } catch (Exception $e) {
                // Fallback to file storage if database fails
                error_log("Database failed, using file storage: " . $e->getMessage());
                $this->useDatabase = false;
                require_once __DIR__ . '/FileStorage.php';
                $this->storage = new FileStorage();
            }
        } else {
            // Use file storage
            require_once __DIR__ . '/FileStorage.php';
            $this->storage = new FileStorage();
        }
    }
    
    /**
     * Save a search query and response
     */
    public function saveSearch($query, $response = null) {
        $query = trim($query);
        if (empty($query)) {
            throw new InvalidArgumentException("Query cannot be empty");
        }
        
        $sql = "INSERT INTO search_history (query, response) VALUES (?, ?)";
        return $this->db->insert($sql, [$query, $response]);
    }
    
    /**
     * Update search response
     */
    public function updateResponse($id, $response) {
        $sql = "UPDATE search_history SET response = ? WHERE id = ?";
        return $this->db->update($sql, [$response, $id]);
    }
    
    /**
     * Get all search history ordered by creation date (newest first)
     */
    public function getAllSearches($limit = 100) {
        $sql = "SELECT * FROM search_history ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
    
    /**
     * Get search by ID
     */
    public function getSearchById($id) {
        $sql = "SELECT * FROM search_history WHERE id = ?";
        return $this->db->fetch($sql, [$id]);
    }
    
    /**
     * Search queries by keyword
     */
    public function searchQueries($keyword, $limit = 50) {
        $keyword = '%' . $keyword . '%';
        $sql = "SELECT * FROM search_history WHERE query LIKE ? OR response LIKE ? ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$keyword, $keyword, $limit]);
    }
    
    /**
     * Delete search by ID
     */
    public function deleteSearch($id) {
        $sql = "DELETE FROM search_history WHERE id = ?";
        return $this->db->delete($sql, [$id]);
    }
    
    /**
     * Get search count
     */
    public function getSearchCount() {
        $sql = "SELECT COUNT(*) as count FROM search_history";
        $result = $this->db->fetch($sql);
        return $result['count'] ?? 0;
    }
    
    /**
     * Get recent searches (last 10)
     */
    public function getRecentSearches($limit = 10) {
        $sql = "SELECT * FROM search_history ORDER BY created_at DESC LIMIT ?";
        return $this->db->fetchAll($sql, [$limit]);
    }
}
