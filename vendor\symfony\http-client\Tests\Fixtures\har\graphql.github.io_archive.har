{"log": {"version": "1.2", "creator": {"name": "Firefox", "version": "114.0.2"}, "browser": {"name": "Firefox", "version": "114.0.2"}, "pages": [{"startedDateTime": "2023-06-28T15:09:54.445+02:00", "id": "page_3", "title": "SWAPI GraphQL API", "pageTimings": {"onContentLoad": -1, "onLoad": -1}}], "entries": [{"pageref": "page_3", "startedDateTime": "2023-06-28T15:09:54.445+02:00", "request": {"bodySize": 0, "method": "OPTIONS", "url": "https://swapi-graphql.netlify.app/graphql", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "swapi-graphql.netlify.app"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "content-type"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://graphql.github.io/"}, {"name": "Origin", "value": "https://graphql.github.io"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}], "cookies": [], "queryString": [], "headersSize": 503}, "response": {"status": 204, "statusText": "No Content", "httpVersion": "HTTP/2", "headers": [{"name": "access-control-allow-headers", "value": "content-type"}, {"name": "access-control-allow-methods", "value": "GET,HEAD,PUT,PATCH,POST,DELETE"}, {"name": "access-control-allow-origin", "value": "*"}, {"name": "age", "value": "0"}, {"name": "cache-control", "value": "no-cache"}, {"name": "date", "value": "Wed, 28 Jun 2023 13:09:54 GMT"}, {"name": "server", "value": "Netlify"}, {"name": "strict-transport-security", "value": "max-age=31536000; includeSubDomains; preload"}, {"name": "vary", "value": "Access-Control-Request-Headers"}, {"name": "x-nf-request-id", "value": "01H411ZVQVBT0YQBM4HVM13M6B"}, {"name": "x-powered-by", "value": "Express"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "text/plain", "size": 0, "text": ""}, "redirectURL": "", "headersSize": 449, "bodySize": 449}, "cache": {}, "timings": {"blocked": 0, "dns": 22, "connect": 26, "ssl": 35, "send": 0, "wait": 332, "receive": 0}, "time": 415, "_securityState": "secure", "serverIPAddress": "2a05:d014:275:cb01::c8", "connection": "443"}, {"pageref": "page_3", "startedDateTime": "2023-06-28T15:09:54.863+02:00", "request": {"bodySize": 140, "method": "POST", "url": "https://swapi-graphql.netlify.app/graphql", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "swapi-graphql.netlify.app"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "application/json"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://graphql.github.io/"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Content-Length", "value": "140"}, {"name": "Origin", "value": "https://graphql.github.io"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "TE", "value": "trailers"}], "cookies": [], "queryString": [], "headersSize": 483, "postData": {"mimeType": "application/json", "params": [], "text": "{\"query\":\"{\\n  allPlanets(first: 5) {\\n    edges {\\n      node {\\n        name\\n        population\\n      }\\n    }\\n    totalCount\\n  }\\n}\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/2", "headers": [{"name": "access-control-allow-origin", "value": "*"}, {"name": "age", "value": "0"}, {"name": "cache-control", "value": "no-cache"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Wed, 28 Jun 2023 13:09:54 GMT"}, {"name": "etag", "value": "W/\"121-7dPlPIsXvusgXWd85SPlxqektH8\""}, {"name": "server", "value": "Netlify"}, {"name": "strict-transport-security", "value": "max-age=31536000; includeSubDomains; preload"}, {"name": "x-nf-request-id", "value": "01H411ZW2AD35VKV3BVKC0B1BR"}, {"name": "x-powered-by", "value": "Express"}, {"name": "content-length", "value": "289"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 289, "text": "{\"data\":{\"allPlanets\":{\"edges\":[{\"node\":{\"name\":\"<PERSON><PERSON><PERSON><PERSON>\",\"population\":200000}},{\"node\":{\"name\":\"Alderaan\",\"population\":2000000000}},{\"node\":{\"name\":\"<PERSON>vin IV\",\"population\":1000}},{\"node\":{\"name\":\"<PERSON><PERSON>\",\"population\":null}},{\"node\":{\"name\":\"Dagobah\",\"population\":null}}],\"totalCount\":60}}}"}, "redirectURL": "", "headersSize": 408, "bodySize": 697}, "cache": {}, "timings": {"blocked": 0, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 140, "receive": 0}, "time": 140, "_securityState": "secure", "serverIPAddress": "2a05:d014:275:cb01::c8", "connection": "443"}, {"pageref": "page_3", "startedDateTime": "2023-06-28T15:10:20.623+02:00", "request": {"bodySize": 0, "method": "OPTIONS", "url": "https://swapi-graphql.netlify.app/graphql", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "swapi-graphql.netlify.app"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "*/*"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "Access-Control-Request-Method", "value": "POST"}, {"name": "Access-Control-Request-Headers", "value": "content-type"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://graphql.github.io/"}, {"name": "Origin", "value": "https://graphql.github.io"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "TE", "value": "trailers"}], "cookies": [], "queryString": [], "headersSize": 503}, "response": {"status": 204, "statusText": "No Content", "httpVersion": "HTTP/2", "headers": [{"name": "access-control-allow-headers", "value": "content-type"}, {"name": "access-control-allow-methods", "value": "GET,HEAD,PUT,PATCH,POST,DELETE"}, {"name": "access-control-allow-origin", "value": "*"}, {"name": "age", "value": "0"}, {"name": "cache-control", "value": "no-cache"}, {"name": "date", "value": "Wed, 28 Jun 2023 13:10:20 GMT"}, {"name": "server", "value": "Netlify"}, {"name": "strict-transport-security", "value": "max-age=31536000; includeSubDomains; preload"}, {"name": "vary", "value": "Access-Control-Request-Headers"}, {"name": "x-nf-request-id", "value": "01H4120N7CTHFH0JGR2CYKWG5S"}, {"name": "x-powered-by", "value": "Express"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "text/plain", "size": 0, "text": ""}, "redirectURL": "", "headersSize": 449, "bodySize": 449}, "cache": {}, "timings": {"blocked": 0, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 164, "receive": 0}, "time": 164, "_securityState": "secure", "serverIPAddress": "2a05:d014:275:cb01::c8", "connection": "443"}, {"pageref": "page_3", "startedDateTime": "2023-06-28T15:10:20.830+02:00", "request": {"bodySize": 137, "method": "POST", "url": "https://swapi-graphql.netlify.app/graphql", "httpVersion": "HTTP/2", "headers": [{"name": "Host", "value": "swapi-graphql.netlify.app"}, {"name": "User-Agent", "value": "Mozilla/5.0 (Windows NT 10.0; rv:114.0) Gecko/20100101 Firefox/114.0"}, {"name": "Accept", "value": "application/json"}, {"name": "Accept-Language", "value": "en-US,en;q=0.5"}, {"name": "Accept-Encoding", "value": "gzip, deflate, br"}, {"name": "<PERSON><PERSON><PERSON>", "value": "https://graphql.github.io/"}, {"name": "Content-Type", "value": "application/json"}, {"name": "Content-Length", "value": "137"}, {"name": "Origin", "value": "https://graphql.github.io"}, {"name": "DNT", "value": "1"}, {"name": "Connection", "value": "keep-alive"}, {"name": "Sec-Fetch-Dest", "value": "empty"}, {"name": "Sec-Fetch-Mode", "value": "cors"}, {"name": "Sec-Fetch-Site", "value": "cross-site"}, {"name": "TE", "value": "trailers"}], "cookies": [], "queryString": [], "headersSize": 483, "postData": {"mimeType": "application/json", "params": [], "text": "{\"query\":\"{\\n  allFilms(first: 5) {\\n    edges {\\n      node {\\n        title\\n        director\\n      }\\n    }\\n    totalCount\\n  }\\n}\"}"}}, "response": {"status": 200, "statusText": "OK", "httpVersion": "HTTP/2", "headers": [{"name": "access-control-allow-origin", "value": "*"}, {"name": "age", "value": "0"}, {"name": "cache-control", "value": "no-cache"}, {"name": "content-type", "value": "application/json; charset=utf-8"}, {"name": "date", "value": "Wed, 28 Jun 2023 13:10:20 GMT"}, {"name": "etag", "value": "W/\"17f-TuxAgZCdCuvdKoW7g+r5k6aKy6Q\""}, {"name": "server", "value": "Netlify"}, {"name": "strict-transport-security", "value": "max-age=31536000; includeSubDomains; preload"}, {"name": "x-nf-request-id", "value": "01H4120NDYJ17J0Q5KKSZRWDY5"}, {"name": "x-powered-by", "value": "Express"}, {"name": "content-length", "value": "383"}, {"name": "X-Firefox-Spdy", "value": "h2"}], "cookies": [], "content": {"mimeType": "application/json; charset=utf-8", "size": 383, "text": "{\"data\":{\"allFilms\":{\"edges\":[{\"node\":{\"title\":\"A New Hope\",\"director\":\"<PERSON>\"}},{\"node\":{\"title\":\"The Empire Strikes Back\",\"director\":\"<PERSON><PERSON>\"}},{\"node\":{\"title\":\"Return of the Jedi\",\"director\":\"<PERSON>\"}},{\"node\":{\"title\":\"The Phantom Menace\",\"director\":\"<PERSON>\"}},{\"node\":{\"title\":\"Attack of the Clones\",\"director\":\"<PERSON>\"}}],\"totalCount\":6}}}"}, "redirectURL": "", "headersSize": 408, "bodySize": 791}, "cache": {}, "timings": {"blocked": -1, "dns": 0, "connect": 0, "ssl": 0, "send": 0, "wait": 204, "receive": 0}, "time": 204, "_securityState": "secure", "serverIPAddress": "2a05:d014:275:cb01::c8", "connection": "443"}]}}