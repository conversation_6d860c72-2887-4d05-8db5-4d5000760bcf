<?php
/**
 * Gemini API Test Page
 */

// Define application constant
define('WEB_RESEARCHER_APP', true);

// Include configuration and dependencies
require_once 'config/config.php';
require_once 'config/database.php';
require_once 'includes/functions.php';
require_once 'classes/Database.php';
require_once 'classes/GeminiAPI.php';

$testResult = null;
$apiStats = null;
$error = null;

// Test API if requested
if (isPost() && getPost('action') === 'test') {
    try {
        $geminiAPI = new GeminiAPI();
        $testQuery = getPost('test_query', 'Hello, please respond with a brief test message to confirm the API is working.');
        
        $result = $geminiAPI->generateContent($testQuery);
        $testResult = $result;
        
        if ($result['success']) {
            setFlashMessage('success', 'API test successful!');
        } else {
            setFlashMessage('error', 'API test failed: ' . $result['error']);
        }
    } catch (Exception $e) {
        $error = $e->getMessage();
        setFlashMessage('error', 'Test error: ' . $error);
    }
}

// Get API statistics
try {
    $geminiAPI = new GeminiAPI();
    $apiStats = $geminiAPI->getApiStats(7);
} catch (Exception $e) {
    error_log("Failed to get API stats: " . $e->getMessage());
}

$flashMessages = getFlashMessages();
?>
<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title><?php echo APP_NAME; ?> - API Test</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            line-height: 1.6;
            color: #333;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }
        
        .container {
            max-width: 1000px;
            margin: 0 auto;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
            color: white;
        }
        
        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
        }
        
        .card {
            background: rgba(255, 255, 255, 0.95);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 30px;
            margin-bottom: 20px;
            box-shadow: 0 10px 30px rgba(0,0,0,0.1);
        }
        
        .nav-links {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .nav-link {
            color: white;
            text-decoration: none;
            padding: 12px 24px;
            border: 2px solid white;
            border-radius: 25px;
            display: inline-block;
            margin: 0 10px;
            transition: all 0.3s ease;
        }
        
        .nav-link:hover {
            background: white;
            color: #667eea;
        }
        
        .test-form {
            margin-bottom: 30px;
        }
        
        .form-group {
            margin-bottom: 20px;
        }
        
        .form-label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #2c3e50;
        }
        
        .form-input {
            width: 100%;
            padding: 15px;
            border: 2px solid #e1e8ed;
            border-radius: 10px;
            font-size: 16px;
            transition: all 0.3s ease;
        }
        
        .form-input:focus {
            outline: none;
            border-color: #667eea;
            box-shadow: 0 0 0 3px rgba(102, 126, 234, 0.1);
        }
        
        .btn {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 30px;
            border: none;
            border-radius: 25px;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
        }
        
        .btn:hover {
            transform: translateY(-2px);
            box-shadow: 0 5px 15px rgba(102, 126, 234, 0.3);
        }
        
        .alert {
            padding: 15px;
            margin-bottom: 20px;
            border-radius: 10px;
            font-weight: 500;
        }
        
        .alert-success {
            background: linear-gradient(135deg, #4CAF50, #45a049);
            color: white;
        }
        
        .alert-error {
            background: linear-gradient(135deg, #f44336, #da190b);
            color: white;
        }
        
        .result-section {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 5px solid #667eea;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-top: 20px;
        }
        
        .stat-item {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            text-align: center;
            border: 2px solid #e9ecef;
        }
        
        .stat-value {
            font-size: 2em;
            font-weight: bold;
            color: #667eea;
            display: block;
        }
        
        .stat-label {
            color: #666;
            font-size: 0.9em;
            margin-top: 5px;
        }
        
        .config-info {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            padding: 15px;
            border-radius: 10px;
            margin-bottom: 20px;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>🤖 Gemini API Test</h1>
            <p>Test and monitor your AI integration (Official PHP Client)</p>
        </div>
        
        <div class="nav-links">
            <a href="index.php" class="nav-link">🏠 Homepage</a>
            <a href="library.php" class="nav-link">📚 Library</a>
            <a href="database_status.php" class="nav-link">🗄️ Database</a>
        </div>
        
        <?php foreach ($flashMessages as $message): ?>
            <div class="alert alert-<?php echo $message['type']; ?>">
                <?php echo sanitizeInput($message['message']); ?>
            </div>
        <?php endforeach; ?>
        
        <div class="card">
            <h3>🔧 API Configuration</h3>
            <div class="config-info">
                <strong>API Key:</strong> <?php echo !empty(GEMINI_API_KEY) ? '✅ Configured (' . substr(GEMINI_API_KEY, 0, 10) . '...)' : '❌ Not configured'; ?><br>
                <strong>Client:</strong> Official Gemini API PHP Client<br>
                <strong>Model:</strong> gemini-pro
            </div>
        </div>
        
        <div class="card">
            <h3>🧪 Test API Connection</h3>
            <form method="POST" class="test-form">
                <input type="hidden" name="action" value="test">
                <?php echo getCSRFTokenField(); ?>
                
                <div class="form-group">
                    <label class="form-label">Test Query:</label>
                    <textarea name="test_query" class="form-input" rows="3" placeholder="Enter a test question for the AI..."><?php echo sanitizeInput(getPost('test_query', 'What is artificial intelligence and how does it work?')); ?></textarea>
                </div>
                
                <button type="submit" class="btn">🚀 Test API</button>
            </form>
            
            <?php if ($testResult): ?>
                <div class="result-section">
                    <h4>📋 Test Result</h4>
                    <p><strong>Status:</strong> <?php echo $testResult['success'] ? '✅ Success' : '❌ Failed'; ?></p>
                    <p><strong>Response Time:</strong> <?php echo $testResult['response_time']; ?>ms</p>
                    
                    <?php if ($testResult['success']): ?>
                        <p><strong>AI Response:</strong></p>
                        <div style="background: white; padding: 15px; border-radius: 10px; margin-top: 10px; white-space: pre-wrap;"><?php echo sanitizeInput($testResult['content']); ?></div>
                    <?php else: ?>
                        <p><strong>Error:</strong> <?php echo sanitizeInput($testResult['error']); ?></p>
                    <?php endif; ?>
                </div>
            <?php endif; ?>
        </div>
        
        <?php if ($apiStats): ?>
        <div class="card">
            <h3>📊 API Usage Statistics (Last 7 Days)</h3>
            <div class="stats-grid">
                <div class="stat-item">
                    <span class="stat-value"><?php echo $apiStats['total_calls'] ?? 0; ?></span>
                    <div class="stat-label">Total Calls</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo $apiStats['successful_calls'] ?? 0; ?></span>
                    <div class="stat-label">Successful</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo $apiStats['failed_calls'] ?? 0; ?></span>
                    <div class="stat-label">Failed</div>
                </div>
                <div class="stat-item">
                    <span class="stat-value"><?php echo round($apiStats['avg_response_time'] ?? 0); ?>ms</span>
                    <div class="stat-label">Avg Response Time</div>
                </div>
            </div>
        </div>
        <?php endif; ?>
    </div>
</body>
</html>
